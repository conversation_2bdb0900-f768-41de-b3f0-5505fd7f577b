package impl

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	cmdTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"
	"github.com/epifi/gamma/api/salaryestimation"
	"github.com/epifi/gamma/salaryestimation/dao"
	"github.com/epifi/gamma/salaryestimation/dao/model"
)

// SalaryEstimationAttemptDaoImpl implements the SalaryEstimationAttemptDao interface
type SalaryEstimationAttemptDaoImpl struct {
	db *gorm.DB
}

var _ dao.SalaryEstimationAttemptDao = &SalaryEstimationAttemptDaoImpl{}

// salaryEstimationAttemptColumnNames maps field masks to database column names
var salaryEstimationAttemptColumnNames = map[salaryestimation.SalaryEstimationAttemptFieldMask]string{
	salaryestimation.SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_STATUS: "status",
}

// NewSalaryEstimationAttemptDao creates a new instance of SalaryEstimationAttemptDaoImpl
func NewSalaryEstimationAttemptDao(db cmdTypes.FeatureEngineeringPGDB) dao.SalaryEstimationAttemptDao {
	return &SalaryEstimationAttemptDaoImpl{
		db: db,
	}
}

// Create creates a new salary estimation attempt record
func (d *SalaryEstimationAttemptDaoImpl) Create(ctx context.Context, attempt *salaryestimation.SalaryEstimationAttempt) (*salaryestimation.SalaryEstimationAttempt, error) {
	defer metric_util.TrackDuration("salaryestimation/dao/impl", "SalaryEstimationAttemptDaoImpl", "Create", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	attemptModel := model.NewSalaryEstimationAttempt(attempt)
	if err := db.Create(attemptModel).Error; err != nil {
		return nil, errors.Wrap(err, "failed to create salary estimation attempt")
	}
	return attemptModel.GetProto(), nil
}

// GetByClientReqID retrieves a salary estimation attempt by client request ID
func (d *SalaryEstimationAttemptDaoImpl) GetByClientReqID(ctx context.Context, clientReqID string) (*salaryestimation.SalaryEstimationAttempt, error) {
	defer metric_util.TrackDuration("salaryestimation/dao/impl", "SalaryEstimationAttemptDaoImpl", "GetByClientReqID", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, d.db)
	query := db.Model(&model.SalaryEstimationAttempt{}).Where("client_req_id = ?", clientReqID)
	var attempt model.SalaryEstimationAttempt
	if err := query.First(&attempt).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.Wrap(epifierrors.ErrRecordNotFound, "salary estimation attempt not found")
		}
		return nil, errors.Wrap(err, "failed to get salary estimation attempt by client request ID")
	}
	return attempt.GetProto(), nil
}

func (d *SalaryEstimationAttemptDaoImpl) Update(
	ctx context.Context,
	attempt *salaryestimation.SalaryEstimationAttempt,
	fieldMasks []salaryestimation.SalaryEstimationAttemptFieldMask,
) (*salaryestimation.SalaryEstimationAttempt, error) {
	defer metric_util.TrackDuration("salaryestimation/dao/impl", "SalaryEstimationAttemptDaoImpl", "Update", time.Now())

	// Validate input parameters
	if attempt.GetId() == "" {
		return nil, fmt.Errorf("primary identifier can't be empty for an update operation")
	}
	if len(fieldMasks) == 0 {
		return nil, fmt.Errorf("update mask can't be empty")
	}

	// Convert proto to model
	attemptModel := model.NewSalaryEstimationAttempt(attempt)

	// Get selected columns for update based on field masks
	updateColumns := d.selectedColumnsForUpdate(fieldMasks)

	// Get database handle
	db := gormctxv2.FromContextOrDefault(ctx, d.db)

	// Perform update with returning clause
	whereClause := &model.SalaryEstimationAttempt{
		ID: attempt.GetId(),
	}

	if err := db.Model(attemptModel).Where(whereClause).Select(updateColumns).Clauses(clause.Returning{}).Updates(attemptModel).Error; err != nil {
		return nil, errors.Wrap(err, "failed to update salary estimation attempt")
	}

	return attemptModel.GetProto(), nil
}

// selectedColumnsForUpdate returns the database column names for the given field masks
func (d *SalaryEstimationAttemptDaoImpl) selectedColumnsForUpdate(fieldMasks []salaryestimation.SalaryEstimationAttemptFieldMask) []string {
	var selectColumns []string
	for _, field := range fieldMasks {
		if columnName, exists := salaryEstimationAttemptColumnNames[field]; exists {
			selectColumns = append(selectColumns, columnName)
		}
	}
	return selectColumns
}
